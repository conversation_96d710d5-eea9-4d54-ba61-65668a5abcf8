class_name Sink
extends ConveyorBasedBuilding


const BUCKET_DURATION: int = 10    # seconds
const NUM_BUCKETS: int = 7         # 7 buckets for smoother 6-bucket average
var buckets: Array[int] = []              # stores event counts per bucket
var bucket_timestamps: Array[int] = []    # stores timestamps for each bucket

# Temporary has iron ingots but should be set in scene when making the level
@export var valid_item_type: ItemType.Enum = ItemType.Enum.IRON_INGOT


func _ready() -> void:
	# Initialize buckets
	buckets.resize(NUM_BUCKETS)
	buckets.fill(0)
	bucket_timestamps.resize(NUM_BUCKETS)
	bucket_timestamps.fill(0)
	
	# Every 10 secs we emit an updated on throughput so that it dies down
	# when no items are being sent
	var timer: Timer = Timer.new()
	timer.wait_time = 10
	timer.one_shot = false
	timer.connect("timeout", _on_timer_timeout)
	add_child(timer)
	timer.start()


# Called when an item is consumed
func _register_item_for_throughput():
	# Get current time in seconds
	var now: int = Time.get_ticks_msec() / 1000
	# Get the index of the bucket based on where in the 7 ten second buckets "now" falls
	var index: int = (now % (BUCKET_DURATION * NUM_BUCKETS)) / BUCKET_DURATION

	# Reset bucket if it's stale
	var bucket_start_time: int = now - (now % BUCKET_DURATION)
	if bucket_timestamps[index] != bucket_start_time:
		buckets[index] = 0
		bucket_timestamps[index] = bucket_start_time

	buckets[index] += 1


# Estimate throughput per minute
func get_throughput_per_minute() -> int:
	var sum: float = 0.0

	for i in range(NUM_BUCKETS):
		sum += buckets[i]

	# Avarage of 7 ten second buckets trimmed back to 6 (because 6 * 10 = 60 seconds or one minute)
	# This is done so that when last bucket switches the jump back is not as insane
	var average: float = (sum / NUM_BUCKETS) * 6
	return int(average)


func initialize_output_coordinates() -> void:
	pass


func _before_process(_delta: float) -> void:
	if _held_item != null and _held_item.is_moving():
		return

	consume_item()


func _on_timer_timeout() -> void:
	BuildingSignalBus.sink_consumed.emit(null, get_throughput_per_minute(), get_instance_id())


func consume_item() -> void:
	if _held_item == null:
		return

	if _held_item.item_data.type == valid_item_type:
		BuildingSignalBus.sink_consumed.emit(_held_item.item_data.type, get_throughput_per_minute(), get_instance_id())
		_register_item_for_throughput()

	_held_item.queue_free()
	_held_item = null
