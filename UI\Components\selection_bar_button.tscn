[gd_scene load_steps=4 format=3 uid="uid://blafm2yh8ieq5"]

[ext_resource type="Script" uid="uid://divkl4aq3qo1x" path="res://UI/Components/selection_bar_button.gd" id="1_kos5w"]
[ext_resource type="Texture2D" uid="uid://tmaenl3xl0gu" path="res://assets/Sprites/32x32/SpriteSheets/Furnace_sprite_sheet_anim.png" id="2_xnl52"]

[sub_resource type="AtlasTexture" id="AtlasTexture_n45dw"]
atlas = ExtResource("2_xnl52")
region = Rect2(0, 0, 32, 32)

[node name="SelectionBarButton" type="PanelContainer"]
offset_right = 136.0
offset_bottom = 136.0
size_flags_horizontal = 0
size_flags_vertical = 0
script = ExtResource("1_kos5w")

[node name="Building Description Container" type="MarginContainer" parent="."]
visible = false
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Building Description Container"]
visible = false
layout_mode = 2

[node name="Building Name" type="Label" parent="Building Description Container/VBoxContainer"]
layout_mode = 2

[node name="Building Description" type="Label" parent="Building Description Container/VBoxContainer"]
layout_mode = 2

[node name="Button" type="TextureButton" parent="."]
custom_minimum_size = Vector2(32, 32)
layout_mode = 2
focus_mode = 0
texture_normal = SubResource("AtlasTexture_n45dw")
ignore_texture_size = true
stretch_mode = 0

[connection signal="mouse_entered" from="." to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
[connection signal="mouse_exited" from="Button" to="." method="_on_button_mouse_exited"]
