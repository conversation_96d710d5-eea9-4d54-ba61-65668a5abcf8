extends Control

@onready var MAIN_MENU = load("res://Scenes/Menus/MainMenu.tscn")
@onready var SPACE_MAP_MENU = load("res://Scenes/Menus/SpaceMapMenu.tscn")

func _on_back_button_pressed() -> void:
	_play_button_click_sound()
	get_tree().change_scene_to_packed(MAIN_MENU)


# TODO change this to be generic and not hard coded
func _on_continue_button_pressed() -> void:
	SaveManager.save_slot_name = "Player 1"
	_play_button_click_sound()
	get_tree().change_scene_to_packed(SPACE_MAP_MENU)
	
	
func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
