@icon("res://assets/IconGodotNode/node_2D/icon_bone.png")

extends Node2D
class_name Item

@export var item_data: ItemData
var temperature: float
var tween: Tween

@onready var item_heat_glow: Sprite2D = $ItemHeatGlow
@onready var item_sprite: Sprite2D = $ItemSprite

signal stats_reloaded

func _ready() -> void:
	reload_stats()


func move_to(target_position: Vector2, transition_time: float) -> void:
	tween = create_tween()
	tween.tween_property(self, "global_position", target_position, transition_time)


func is_moving() -> bool:
	return tween != null and tween.is_running()


func reload_stats() -> void:
	if item_data == null:
		push_error("No item data was passed")
		return
	
	$ItemSprite.texture = item_data.sprite
	temperature = item_data.temperature
	
	stats_reloaded.emit()

func _process(delta: float) -> void:
	item_sprite.material.set_shader_parameter("temperature", temperature)
	item_heat_glow.material.set_shader_parameter("temperature", temperature)
	
	_simulate_burning(delta)
	_simulate_cooling(delta)

func _simulate_cooling(delta: float):
	# Simulates material cooling down
	const cooling_coeficient := 1.0/10
	temperature = lerpf(temperature, 20, cooling_coeficient * delta)

func _simulate_burning(delta: float):
	# Simulates material burning
	pass
