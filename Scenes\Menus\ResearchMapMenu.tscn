[gd_scene load_steps=12 format=3 uid="uid://c1xx6uyo7bcwx"]

[ext_resource type="Script" uid="uid://ctaxwanh5hjy" path="res://Scenes/Menus/research_map_menu.gd" id="1_xbljl"]
[ext_resource type="Script" uid="uid://d2s7r1nqci5th" path="res://Entities/NodeMap/space_camera.gd" id="2_m6klo"]
[ext_resource type="Texture2D" uid="uid://cnc8wrrgojiw8" path="res://Assets/Sprites/TempBack.png" id="3_s7jso"]
[ext_resource type="Texture2D" uid="uid://byxx30nvmbm05" path="res://Assets/Sprites/TempBackHoverClicked.png" id="4_t3dtw"]
[ext_resource type="Script" uid="uid://bad08elc438by" path="res://Scenes/camera_limiter.gd" id="5_0tyyp"]
[ext_resource type="LabelSettings" uid="uid://dybh11bdm6mcy" path="res://Assets/Text/Header.tres" id="6_m6klo"]
[ext_resource type="PackedScene" uid="uid://jt2vkf6rx6f4" path="res://Entities/NodeMap/ResearchMap/research_node.tscn" id="6_spbkr"]
[ext_resource type="Script" uid="uid://kiuh738llx2t" path="res://Entities/Items/item_recipe.gd" id="8_t3dtw"]
[ext_resource type="Resource" uid="uid://dgu230prgtch8" path="res://Entities/Items/Resources/Processed/silicon_recipe.tres" id="9_0tyyp"]
[ext_resource type="Script" uid="uid://c7s4elarc1emd" path="res://Entities/Buildings/building base/building_stats.gd" id="10_spbkr"]
[ext_resource type="Resource" uid="uid://bngmej0pvatss" path="res://Entities/Buildings/item generator/item_generator.tres" id="11_g4blk"]

[node name="ResearchMapMenu" type="Node2D"]
script = ExtResource("1_xbljl")

[node name="Camera2D" type="Camera2D" parent="." node_paths=PackedStringArray("border")]
position = Vector2(962, -724)
script = ExtResource("2_m6klo")
border = NodePath("../CameraLimiter")

[node name="BackButton" type="TextureButton" parent="Camera2D"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = 857.0
offset_top = -504.0
offset_right = 921.0
offset_bottom = -440.0
grow_horizontal = 0
texture_normal = ExtResource("3_s7jso")
texture_pressed = ExtResource("4_t3dtw")
texture_hover = ExtResource("4_t3dtw")
stretch_mode = 4

[node name="CameraLimiter" type="ReferenceRect" parent="."]
offset_left = -100.0
offset_top = -85.0
offset_right = 1541.0
offset_bottom = 805.0
mouse_filter = 2
border_width = 3.0
editor_only = false
script = ExtResource("5_0tyyp")

[node name="Label" type="Label" parent="CameraLimiter"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -148.0
offset_right = 148.0
offset_bottom = 45.0
grow_horizontal = 2
text = "RESEARCH SELECTION"
label_settings = ExtResource("6_m6klo")

[node name="ResearchNode" parent="." node_paths=PackedStringArray("linked_nodes") instance=ExtResource("6_spbkr")]
position = Vector2(542, 209)
recipes_to_unlock = Array[ExtResource("8_t3dtw")]([ExtResource("9_0tyyp")])
buildings_to_unlock = Array[ExtResource("10_spbkr")]([ExtResource("11_g4blk")])
NODE_INIT_STATE = 1
linked_nodes = [NodePath("../ResearchNode2")]

[node name="ResearchNode2" parent="." node_paths=PackedStringArray("linked_nodes") instance=ExtResource("6_spbkr")]
position = Vector2(807, 591)
linked_nodes = [NodePath("../ResearchNode3")]

[node name="ResearchNode3" parent="." instance=ExtResource("6_spbkr")]
position = Vector2(1165, 237)

[connection signal="pressed" from="Camera2D/BackButton" to="." method="_on_back_button_pressed"]
