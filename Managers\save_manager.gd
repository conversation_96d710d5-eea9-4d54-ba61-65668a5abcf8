extends Node

var save_slot_name: String
var selected_planet_name: String
var Loaded_save: PackedScene

const SAVE_ROOT = "user://saves"

var save_path: String:
	get():
		return "%s/%s" % [ SAVE_ROOT, save_slot_name ]

var planet_save_path: String:
	get():
		return "%s/%s.tscn" % [ save_path, selected_planet_name ]

func try_load_planet_map() -> void:
	if FileAccess.file_exists(planet_save_path):
		Loaded_save = load(planet_save_path)
	else:
		print("Save file [%s] does not exist." % planet_save_path)
		return

	var buildings_node: Node2D = get_tree().get_current_scene().get_node("Buildings")
	var instance: Node2D = Loaded_save.instantiate()
	for bulding: Building in instance.get_children():
		bulding.bind_stats()
		bulding.reparent(buildings_node)
		bulding.owner = buildings_node
		if bulding is ItemHandlingBuilding:
			for item: Item in bulding.get_output_items():
				if item != null:
					item.reload_stats()
		# TODO put this in bulding.initialize()
		BuildingModeManager.occupied_tiles[bulding.tile_coordinates] = bulding
		BuildingModeManager._sync_animation(bulding, true)
	
	print("Loaded save: [%s]" % planet_save_path)


func save_planet_map() -> void:
	var dir = DirAccess.open("user://saves/Player 1")
	if not dir:
		dir = DirAccess.make_dir_recursive_absolute("user://saves/Player 1")
	
	var buildings = get_tree().get_current_scene().get_node("Buildings")
	var scene = PackedScene.new()
	scene.pack(buildings)
	var save_result = ResourceSaver.save(scene, planet_save_path)
	
	print("Saving planet: [%s]" % planet_save_path)
	if save_result != OK:
		push_error("Failed to save scene!")
