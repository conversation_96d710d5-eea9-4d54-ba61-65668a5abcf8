[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=8 format=3 uid="uid://ba0r520cofryn"]

[ext_resource type="SpriteFrames" uid="uid://fw6adbajlg0s" path="res://Assets/Sprites/Animations/64x64/Assembler.tres" id="1_reyhi"]
[ext_resource type="Script" uid="uid://bobltf8e6ffhp" path="res://Entities/Buildings/_complex_processing/complex_processing_building.gd" id="2_ubnub"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="3_1kygw"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_consumer_component.gd" id="4_q0ymo"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="5_dicki"]
[ext_resource type="Texture2D" uid="uid://7pcwr3stjf1" path="res://Assets/Sprites/32x32/SpriteSheets/Assembler_sprite_sheet_anim.png" id="6_cxo56"]

[sub_resource type="AtlasTexture" id="AtlasTexture_64wys"]
atlas = ExtResource("6_cxo56")
region = Rect2(0, 0, 64, 64)

[resource]
resource_name = "Saw"
script = ExtResource("5_dicki")
input_directions = 10
output_directions = 5
transport_speed = 1.0
translation_key = ""
dimensions = Vector2i(2, 2)
is_rotateable = true
building_type = 11
component_scripts = Array[Script]([ExtResource("3_1kygw"), ExtResource("4_q0ymo")])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_64wys")
animation_frames = ExtResource("1_reyhi")
building_script = ExtResource("2_ubnub")
unlocked_by_default = false
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
