@tool
extends Node2D

## Dictionary of Simple building recipies (only one resource as input)
var simple_recipes_catalog: Dictionary[BuildingType.Enum, Dictionary]

## Dictionary of Complex building recipies (multiple resources as input)
var complex_recipes_catalog: Dictionary[BuildingType.Enum, Array]


## Return Simple building recipies (only one resource as input) 
func get_simple_recipe(building: BuildingType.Enum, input: ItemData) -> ItemRecipe:
	var building_recipes: Dictionary = simple_recipes_catalog.get(building, {})
	var recipe: ItemRecipe = building_recipes.get(input)
	return recipe


## Return Complex building recipies (multiple resources as input)
func get_complex_recipes(building: BuildingType.Enum) -> Array[ItemRecipe]:
	var building_recipes: Array[ItemRecipe] = complex_recipes_catalog.get(building, {})
	return building_recipes


## Checks if given recipe can be crafted
func has_enought_resources_for_recipes(recipe: ItemRecipe, items_available: Dictionary[ItemData, int]) -> bool:
	var required_items := recipe.input_resources
	for required_key in required_items.keys():
		var available_count: int = items_available.get(required_key, 0)
		var required_count: int = required_items.get(required_key)
		
		if available_count < required_count:
			return false
	
	return true


## Substract production cost of item (does not check if items are available)
func substract_cost_for_recipes(recipe: ItemRecipe, items_available: Dictionary[ItemData, int]):
	var required_items := recipe.input_resources
	for required_key in required_items.keys():
		var available_count: int = items_available.get(required_key)
		var required_count: int = required_items.get(required_key)
		
		items_available[required_key] = available_count - required_count


func _ready() -> void:
	# TODO may be some lazy loading in future would be nice
	_get_all_recipes("res://Entities/Items/Resources/")
	# print_all_recipes()

## Print all recipies
func print_all_recipes() -> void:
	print("Simple recipes:")
	for building_recipes in simple_recipes_catalog.values():
		print(building_recipes.values())

	print("Complex recipes:")
	for building_recipes in complex_recipes_catalog.values():
		print(building_recipes)

func print(recipes: Array) -> void:
	for recipe: ItemRecipe in recipes:
		var building_name := BuildingType.to_name(recipe.processing_building)
		var input_items := recipe.input_resources
		var output_items := recipe.output_resources
		
		var line = "\t" + building_name.rpad(10) + str(input_items).lpad(50) + " → " + str(output_items)
		print(line)

## Loads all recipes from folder to dictionaries
func _get_all_recipes(path: String) -> void:
	var file_paths = ResourceFinder.find(path, ".tres")
	
	for file_path in file_paths:
		var res := ResourceLoader.load(file_path)
		if res != null and res is ItemRecipe:
			_append_sorted(res)


# Helper function to sort them corectly
func _append_sorted(recipe: ItemRecipe) -> void:
	var building := recipe.processing_building
	
	if recipe.input_resources.size() <= 1:
		var building_recipes: Dictionary = simple_recipes_catalog.get_or_add(building, {})
		var input_item: ItemData = recipe.input_resources.keys().front()
		if building_recipes.has(input_item):
			push_error("Recipe conflict detected ! (%s x %s)" % [building_recipes[input_item].resource_name, recipe.resource_name])
		building_recipes[input_item] = recipe
	else:
		var building_recipes: Array = complex_recipes_catalog.get_or_add(building, [])
		building_recipes.push_back(recipe)
