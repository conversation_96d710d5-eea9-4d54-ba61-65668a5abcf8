extends Camera2D

@export_category("Camera Parameters")
@export var release_falloff: int = 35
@export var acceleration: int = 100
@export var max_speed: int = 20

@export_category("Zoom Configuration")
@export var max_zoom_out: float = 10.0
@export var max_zoom_in: float = 1.0

@export_category("Camera Boundary")
@export var border: ReferenceRect

var velocity := Vector2.ZERO

var dragging := false
var drag_mouse_origin := Vector2.ZERO
var drag_camera_origin := Vector2.ZERO

func _ready():
	# Set correct zoom
	var zoom_vector := get_camera_zoom_to_map()
	set_zoom(zoom_vector)

	# Center camera on map
	center_camera_on(border.position + border.size / 2)


func center_camera_on(pos: Vector2):
	var max_viewport_size := get_viewport_to_zoom_scale()
	if anchor_mode == Camera2D.ANCHOR_MODE_FIXED_TOP_LEFT:
		global_position = pos - max_viewport_size / 2
	else:
		global_position = pos


func _process(delta: float):
	# Normal camera movement via input
	var input_vector := Input.get_vector("camera_move_left", "camera_move_right", "camera_move_up", "camera_move_down")
	calculate_velocity(input_vector, delta)
	update_global_position(delta)

	# Zoom input
	var zoom_diff: float = 0.0
	if Input.is_action_just_pressed(&"zoom_in"):
		zoom_diff += 0.1
	if Input.is_action_just_pressed(&"zoom_out"):
		zoom_diff -= 0.1

	var current_zoom := get_zoom()
	var new_zoom     := Vector2(
							clamp(current_zoom.x + zoom_diff, max_zoom_out, max_zoom_in),
							clamp(current_zoom.y + zoom_diff, max_zoom_out, max_zoom_in)
						)
	set_zoom(new_zoom)
	
	# Middle mouse drag movement
	if Input.is_mouse_button_pressed(MOUSE_BUTTON_MIDDLE):
		if not dragging:
			# First frame: save both the mouse's world position and the camera's current position
			drag_mouse_origin = get_viewport().get_mouse_position()
			drag_camera_origin = global_position
			dragging = true
		else:
			# Compute offset from original mouse pos and set position based on that offset
			var drag_current := get_viewport().get_mouse_position()
			var offset := drag_mouse_origin - drag_current
			global_position = drag_camera_origin + offset
	else:
		dragging = false


func update_global_position(delta: float):
	global_position += lerp(
		velocity,
		Vector2.ZERO,
		pow(2, -32 * delta)
	)

	global_position = limit_pos_to_screen(global_position)


func limit_pos_to_screen(pos: Vector2) -> Vector2:
	var zoomed_viewport_size := get_viewport_to_zoom_scale()

	# Restricts camera movment to border rectangle - size of viewport
	var left_limit   := border.global_position.x + zoomed_viewport_size.x / 2
	var right_limit  := border.global_position.x + border.size.x - zoomed_viewport_size.x / 2
	var top_limit    := border.global_position.y + zoomed_viewport_size.y / 2
	var bottom_limit := border.global_position.y + border.size.y - zoomed_viewport_size.y / 2

	# Calculates offset for centering the camera
	var pos_offset := Vector2.ZERO
	if anchor_mode != Camera2D.ANCHOR_MODE_DRAG_CENTER:
		pos_offset = zoomed_viewport_size / 2

	# Clamps position of camera
	var x_pos: float = clamp(pos.x + pos_offset.x, left_limit, right_limit)
	var y_pos: float = clamp(pos.y + pos_offset.y, top_limit, bottom_limit)

	# Handle edge case where camera does not fit required area
	if left_limit > right_limit:
		x_pos = (left_limit+right_limit) / 2
	if top_limit > bottom_limit:
		y_pos = (top_limit+bottom_limit) / 2

	return Vector2(
		x_pos - pos_offset.x,
		y_pos - pos_offset.y
	)


func get_viewport_to_zoom_scale() -> Vector2:
	var zoom_vector          := get_zoom()
	var zoomed_viewport_size := Vector2i(
								   get_viewport().size[0] / zoom_vector.x,
								   get_viewport().size[1] / zoom_vector.y,
							   )
	return zoomed_viewport_size


func calculate_velocity(direction: Vector2, delta: float):
	velocity += direction * acceleration * delta

	if direction.x == 0:
		velocity.x = lerp(0.0, velocity.x, pow(2, -release_falloff * delta))
	if direction.y == 0:
		velocity.y = lerp(0.0, velocity.y, pow(2, -release_falloff * delta))

	velocity.x = clamp(
		velocity.x,
		-max_speed,
		max_speed
	)

	velocity.y = clamp(
		velocity.y,
		-max_speed,
		max_speed
	)



func get_camera_zoom_to_map() -> Vector2:
	var viewport_size: Vector2 = get_viewport().size # [x, y]

	var viewport_aspect: float =  float(viewport_size[0]) / viewport_size[1]
	var level_size: Vector2 = border.size
	var level_aspect: float = float(level_size.x) / level_size.y

	var new_zoom: float = 1.0

	if level_aspect > viewport_aspect:
		new_zoom = float(viewport_size[1]) / level_size.y
	else:
		new_zoom = float(viewport_size[0]) / level_size.x

	print("Calculated max zoom %f, base camera limits <%f %f>" % [new_zoom, max_zoom_in, max_zoom_out])
	max_zoom_out = min(new_zoom,max_zoom_out)
	new_zoom = clamp(new_zoom, max_zoom_out, max_zoom_in)

	return Vector2(new_zoom, new_zoom)
