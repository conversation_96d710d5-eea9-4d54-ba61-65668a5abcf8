@tool
class_name SelectionBarButton
extends Control

@export var stats: BuildingStats
# @export var building_script: Script
# @export var building: PackedScene

@onready var target_node: Node = null
var info_box: SelectionBoxInfo = null
@onready var INFO_BOX_PREFAB: PackedScene = load("res://UI/Components/selection_info_box.tscn")


func _ready() -> void:
	assert (stats != null)
	$Button.texture_normal = stats.texture
	$Button.pressed.connect(_on_button_pressed)
	$Button.mouse_entered.connect(_on_mouse_entered)


func _on_mouse_entered() -> void:
	info_box = INFO_BOX_PREFAB.instantiate()
	info_box.reload_stats(stats)
	$"/root/PlanetSurface/Camera2D/UI/HUD".add_child(info_box)
	info_box.position = global_position
	info_box.position.y -= (info_box.get_rect().size.y / 2) + get_rect().size.y
	info_box.position.x += info_box.get_rect().size.x / 4
	$Button.mouse_exited.connect(info_box._on_mouse_exited)
	info_box.show()


func _on_button_pressed() -> void:
	assert (stats != null)

	BuildingModeManager.selected_building_stats = stats
	StateManager.state=StateManager.States.STATE_BUILD
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)

	StateManager.state = StateManager.States.STATE_BUILD

	BuildingSignalBus.building_mode_triggered.emit()
