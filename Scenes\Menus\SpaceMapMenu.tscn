[gd_scene load_steps=10 format=3 uid="uid://ck5jwl6m0hosg"]

[ext_resource type="Script" uid="uid://btc6te8igj7f8" path="res://Scenes/Menus/space_map_menu.gd" id="1_d61je"]
[ext_resource type="Script" uid="uid://d2s7r1nqci5th" path="res://Entities/NodeMap/space_camera.gd" id="1_xws8f"]
[ext_resource type="Script" uid="uid://bad08elc438by" path="res://Scenes/camera_limiter.gd" id="2_5e1ev"]
[ext_resource type="PackedScene" uid="uid://bm2niqbcccmqu" path="res://Entities/NodeMap/SpaceMap/planet_node.tscn" id="2_8u05b"]
[ext_resource type="Texture2D" uid="uid://cnc8wrrgojiw8" path="res://assets/Sprites/TempBack.png" id="3_a6bim"]
[ext_resource type="PackedScene" uid="uid://0ihbkwcfs6yp" path="res://Scenes/Playable/PlanetSurface.tscn" id="3_xws8f"]
[ext_resource type="Texture2D" uid="uid://byxx30nvmbm05" path="res://assets/Sprites/TempBackHoverClicked.png" id="4_u03hk"]
[ext_resource type="Material" uid="uid://bc3n5r7ecf8f1" path="res://Shaders/StarBacgroundMaterial.tres" id="5_4ia1w"]
[ext_resource type="LabelSettings" uid="uid://dybh11bdm6mcy" path="res://assets/Text/Header.tres" id="6_a6bim"]

[node name="SpaceMapMenu" type="Node2D"]
script = ExtResource("1_d61je")

[node name="Camera2D" type="Camera2D" parent="." node_paths=PackedStringArray("border")]
script = ExtResource("1_xws8f")
border = NodePath("../CameraLimiter")

[node name="BackButton" type="TextureButton" parent="Camera2D"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = 857.0
offset_top = -504.0
offset_right = 921.0
offset_bottom = -440.0
grow_horizontal = 0
texture_normal = ExtResource("3_a6bim")
texture_pressed = ExtResource("4_u03hk")
texture_hover = ExtResource("4_u03hk")
stretch_mode = 4

[node name="ExitToMenuButton" type="Button" parent="Camera2D"]
offset_left = 750.0
offset_top = 474.0
offset_right = 941.0
offset_bottom = 524.0
theme_override_font_sizes/font_size = 30
text = "Exit to menu"

[node name="CanvasLayer" type="CanvasLayer" parent="Camera2D"]
layer = -1

[node name="ColorRect" type="ColorRect" parent="Camera2D/CanvasLayer"]
z_index = 1
z_as_relative = false
material = ExtResource("5_4ia1w")
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="CameraLimiter" type="ReferenceRect" parent="."]
offset_left = -100.0
offset_top = -85.0
offset_right = 1553.0
offset_bottom = 792.0
mouse_filter = 2
border_width = 3.0
editor_only = false
script = ExtResource("2_5e1ev")

[node name="Label" type="Label" parent="CameraLimiter"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -148.0
offset_right = 148.0
offset_bottom = 45.0
grow_horizontal = 2
text = "PLANET SELECTION"
label_settings = ExtResource("6_a6bim")

[node name="Planet1" parent="." node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
planet_scene = ExtResource("3_xws8f")
NODE_INIT_STATE = 2
linked_nodes = [NodePath("../Planet2"), NodePath("../Planet3")]

[node name="Planet2" parent="." instance=ExtResource("2_8u05b")]
position = Vector2(276, 470)

[node name="Planet3" parent="." node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(680, 137)
linked_nodes = [NodePath("../Planet4")]

[node name="Planet4" parent="." node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(1144, 202)
linked_nodes = [NodePath("../Planet5")]

[node name="Planet5" parent="." instance=ExtResource("2_8u05b")]
position = Vector2(1439, 689)

[connection signal="pressed" from="Camera2D/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="Camera2D/ExitToMenuButton" to="." method="_on_exit_to_menu_button_pressed"]
