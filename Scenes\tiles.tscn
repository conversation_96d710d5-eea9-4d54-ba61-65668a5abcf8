[gd_scene load_steps=4 format=4 uid="uid://dsyrj2krswx6n"]

[ext_resource type="TileSet" uid="uid://dhhud1rj5v4oj" path="res://assets/Resources.tres" id="2_k6t24"]
[ext_resource type="Script" uid="uid://br2ge3fw75uie" path="res://Scripts/collider_modifier.gd" id="3_7ivye"]

[sub_resource type="TileSet" id="TileSet_k6t24"]
tile_size = Vector2i(32, 32)

[node name="Tiles" type="Node2D"]
z_index = -10

[node name="Ground" type="TileMapLayer" parent="."]
position = Vector2(0, 1)
tile_set = SubResource("TileSet_k6t24")
script = ExtResource("3_7ivye")

[node name="Foreground" type="TileMapLayer" parent="."]
navigation_enabled = false

[node name="Resources" type="TileMapLayer" parent="."]
tile_map_data = PackedByteArray("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")
tile_set = ExtResource("2_k6t24")

[node name="Preview" type="TileMapLayer" parent="."]
modulate = Color(0.7, 0.7, 0.7, 0.784314)
collision_enabled = false
navigation_enabled = false
