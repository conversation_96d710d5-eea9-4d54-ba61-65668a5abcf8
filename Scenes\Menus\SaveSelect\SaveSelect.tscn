[gd_scene load_steps=11 format=3 uid="uid://br4slxgo2l00w"]

[ext_resource type="Script" uid="uid://x3ktnf48ek14" path="res://Scenes/Menus/SaveSelect/save_select.gd" id="1_lic4x"]
[ext_resource type="StyleBox" uid="uid://8en3eownniur" path="res://Assets/Styles/GenericButtonHovered.tres" id="1_sdub3"]
[ext_resource type="StyleBox" uid="uid://br1jxnl5ox4pr" path="res://Assets/Styles/GenericButtonClicked.tres" id="2_5qp3w"]
[ext_resource type="StyleBox" uid="uid://byicfcubdmq4h" path="res://Assets/Styles/GenericButton.tres" id="3_5dhir"]
[ext_resource type="Texture2D" uid="uid://cnc8wrrgojiw8" path="res://Assets/Sprites/TempBack.png" id="4_5dhir"]
[ext_resource type="Texture2D" uid="uid://byxx30nvmbm05" path="res://Assets/Sprites/TempBackHoverClicked.png" id="5_lic4x"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_68561"]
bg_color = Color(0, 0, 0, 1)
border_width_left = 100
border_width_top = 100
border_width_right = 100
border_width_bottom = 100
border_color = Color(0.135617, 0.135617, 0.135617, 1)
border_blend = true

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ge43p"]
bg_color = Color(0.6, 0.6, 0.6, 0)
border_width_left = 4
border_width_top = 4
border_width_right = 4
border_width_bottom = 4

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lic4x"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_68561"]

[node name="SaveSelect" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_lic4x")

[node name="BackGround" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_68561")

[node name="Control" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="HBoxContainer" type="HBoxContainer" parent="Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -502.0
offset_top = -304.0
offset_right = 502.0
offset_bottom = 304.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 50

[node name="VBoxContainer" type="VBoxContainer" parent="Control/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 4
theme_override_constants/separation = 50

[node name="SaveFile1" type="Panel" parent="Control/HBoxContainer/VBoxContainer"]
custom_minimum_size = Vector2(500, 200)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ge43p")

[node name="VBoxContainer" type="VBoxContainer" parent="Control/HBoxContainer/VBoxContainer/SaveFile1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TotalStarLabel" type="Label" parent="Control/HBoxContainer/VBoxContainer/SaveFile1/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.778942, 0.687775, 0.241153, 1)
theme_override_font_sizes/font_size = 20
text = "Total stars:"
horizontal_alignment = 1

[node name="TotalStarNumber" type="Label" parent="Control/HBoxContainer/VBoxContainer/SaveFile1/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "42"
horizontal_alignment = 1

[node name="PlaytimeLabel" type="Label" parent="Control/HBoxContainer/VBoxContainer/SaveFile1/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.403538, 0.732483, 0.306802, 1)
theme_override_font_sizes/font_size = 20
text = "Playtime:"
horizontal_alignment = 1

[node name="PlaytimeNumber" type="Label" parent="Control/HBoxContainer/VBoxContainer/SaveFile1/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "69h 42m 33s"
horizontal_alignment = 1

[node name="ContinueButton" type="Button" parent="Control/HBoxContainer/VBoxContainer/SaveFile1/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_lic4x")
theme_override_styles/hover = ExtResource("1_sdub3")
theme_override_styles/pressed = ExtResource("2_5qp3w")
theme_override_styles/normal = ExtResource("3_5dhir")
text = "Continue"

[node name="SaveFile2" type="Panel" parent="Control/HBoxContainer/VBoxContainer"]
custom_minimum_size = Vector2(500, 200)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ge43p")

[node name="NewGameButton" type="Button" parent="Control/HBoxContainer/VBoxContainer/SaveFile2"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -41.0
offset_top = -11.5
offset_right = 41.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_hover_pressed_color = Color(1, 1, 1, 1)
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_68561")
theme_override_styles/hover = ExtResource("1_sdub3")
theme_override_styles/pressed = ExtResource("2_5qp3w")
theme_override_styles/normal = ExtResource("3_5dhir")
text = "New game"

[node name="SaveFile3" type="Panel" parent="Control/HBoxContainer/VBoxContainer"]
custom_minimum_size = Vector2(500, 200)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ge43p")

[node name="NewGameButton" type="Button" parent="Control/HBoxContainer/VBoxContainer/SaveFile3"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -41.0
offset_top = -11.5
offset_right = 41.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_hover_pressed_color = Color(1, 1, 1, 1)
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_68561")
theme_override_styles/hover = ExtResource("1_sdub3")
theme_override_styles/pressed = ExtResource("2_5qp3w")
theme_override_styles/normal = ExtResource("3_5dhir")
text = "New game"

[node name="VBoxContainer2" type="VBoxContainer" parent="Control/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 4
theme_override_constants/separation = 50

[node name="SaveFile4" type="Panel" parent="Control/HBoxContainer/VBoxContainer2"]
custom_minimum_size = Vector2(500, 200)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ge43p")

[node name="NewGameButton" type="Button" parent="Control/HBoxContainer/VBoxContainer2/SaveFile4"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -41.0
offset_top = -11.5
offset_right = 41.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_hover_pressed_color = Color(1, 1, 1, 1)
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_68561")
theme_override_styles/hover = ExtResource("1_sdub3")
theme_override_styles/pressed = ExtResource("2_5qp3w")
theme_override_styles/normal = ExtResource("3_5dhir")
text = "New game"

[node name="SaveFile5" type="Panel" parent="Control/HBoxContainer/VBoxContainer2"]
custom_minimum_size = Vector2(500, 200)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ge43p")

[node name="NewGameButton" type="Button" parent="Control/HBoxContainer/VBoxContainer2/SaveFile5"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -41.0
offset_top = -11.5
offset_right = 41.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_hover_pressed_color = Color(1, 1, 1, 1)
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_68561")
theme_override_styles/hover = ExtResource("1_sdub3")
theme_override_styles/pressed = ExtResource("2_5qp3w")
theme_override_styles/normal = ExtResource("3_5dhir")
text = "New game"

[node name="SaveFile6" type="Panel" parent="Control/HBoxContainer/VBoxContainer2"]
custom_minimum_size = Vector2(500, 200)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ge43p")

[node name="NewGameButton" type="Button" parent="Control/HBoxContainer/VBoxContainer2/SaveFile6"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -41.0
offset_top = -11.5
offset_right = 41.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_hover_pressed_color = Color(1, 1, 1, 1)
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_68561")
theme_override_styles/hover = ExtResource("1_sdub3")
theme_override_styles/pressed = ExtResource("2_5qp3w")
theme_override_styles/normal = ExtResource("3_5dhir")
text = "New game"

[node name="BackButton" type="TextureButton" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -81.0
offset_top = 16.0
offset_right = -17.0
offset_bottom = 80.0
grow_horizontal = 0
texture_normal = ExtResource("4_5dhir")
texture_pressed = ExtResource("5_lic4x")
texture_hover = ExtResource("5_lic4x")
stretch_mode = 4

[connection signal="pressed" from="Control/HBoxContainer/VBoxContainer/SaveFile1/VBoxContainer/ContinueButton" to="." method="_on_continue_button_pressed"]
[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
